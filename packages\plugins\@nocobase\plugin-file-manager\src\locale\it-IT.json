{"File manager": "Gestore file", "Attachment": "Allegato", "MIME type": "Tipo MIME", "Storage name": "Nome archivio", "Storage type": "Tipo archivio", "Default storage": "<PERSON><PERSON><PERSON> predefinito", "Destination": "Destinazione", "Use the built-in static file server": "Usa il server di file statici integrato", "Local storage": "Archivio locale", "Aliyun OSS": "Aliyun OSS", "Tencent COS": "Tencent COS", "Amazon S3": "Amazon S3", "Region": "Regione", "Bucket": "Bucket", "Path": "<PERSON><PERSON><PERSON>", "Filename": "Nome file", "See more": "<PERSON><PERSON><PERSON>", "Will be used for API": "Sarà utilizzato per l'API", "File collection": "Raccolta file", "File name": "Nome file", "Extension name": "Nome estensione", "Size": "Dimensione file", "File size limit": "Limite dimensione file", "Minimum from 1 byte, maximum up to 1GB.": "Minimo da 1 byte, massimo fino a 1GB.", "File type (in MIME type format)": "Tipo di file (in formato MIME)", "Multi-types seperated with comma, for example: \"image/*\", \"image/png\", \"image/*, application/pdf\" etc.": "Più tipi separati da virgola, ad esempio: \"image/*\", \"image/png\", \"image/*, application/pdf\" ecc.", "URL": "URL", "File storage": "Archiviazione file", "Allow uploading multiple files": "Consenti caricamento di più file", "Storage": "Spazio di archiviazione", "Storages": "Spazi di archiviazione", "Base URL": "URL base", "Base URL for file access, could be your CDN base URL. For example: \"https://cdn.nocobase.com\".": "URL base per l'accesso ai file, potrebbe essere l'URL base del tuo CDN. Ad esempio: \"https://cdn.nocobase.com\".", "Relative path the file will be saved to. Left blank as root path. The leading and trailing slashes \"/\" will be ignored. For example: \"user/avatar\".": "Percorso relativo in cui verrà salvato il file. Lasciare vuoto per il percorso radice. Le barre iniziali e finali \"/\" verranno ignorate. Ad esempio: \"user/avatar\".", "Default storage will be used when not selected": "Se non selezionato verrà utilizzato lo spazio di archiviazione predefinito", "Keep file in storage when destroy record": "Quando elimini il record mantieni il file nello spazio di archiviazione", "Aliyun OSS region part of the bucket. For example: \"oss-cn-beijing\".": "Parte della regione Aliyun OSS del bucket. Ad esempio: \"oss-cn-beijing\"."}