{"name": "@nocobase/plugin-workflow-dynamic-calculation", "displayName": "Workflow: Dynamic calculation node", "displayName.zh-CN": "工作流：动态表达式计算节点", "description": "Useful plugin for doing dynamic calculation based on expression collection records in workflow.", "description.zh-CN": "用于在工作流中进行基于数据行的动态表达式计算。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow-dynamic-calculation", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow-dynamic-calculation", "devDependencies": {"@ant-design/icons": "5.x", "@formily/antd-v5": "1.x", "@formily/core": "2.x", "@formily/react": "2.x", "antd": "5.x", "lodash": "4.17.21", "react": "18.x", "react-i18next": "^11.15.1", "react-router-dom": "^6.11.2"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/evaluators": "1.x", "@nocobase/plugin-data-source-main": "1.x", "@nocobase/plugin-workflow": ">=0.17.0-alpha.3", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Workflow", "Collections"]}