{"File collection": "Bestandcollectie", "File name": "Bestandsnaam", "Extension name": "Extensienaam", "Size": "Grootte", "File size limit": "Bestandsgrootte limiet", "Minimum from 1 byte, maximum up to 1GB.": "Minimaal vanaf 1 byte, maximaal tot 1 GB.", "MIME type": "MIME type", "File type (in MIME type format)": "Bestandstype (in MIME type formaat)", "Multi-types seperated with comma, for example: \"image/*\", \"image/png\", \"image/*, application/pdf\" etc.": "Meerdere typen gescheiden door een komma, bijvoorbeeld: \"image/*\", \"image/png\", \"image/*, application/pdf\" enz.", "URL": "URL", "File storage": "Bestandsopslag", "File manager": "Bestandsbeheerder", "Attachment": "B<PERSON>jlage", "Allow uploading multiple files": "<PERSON><PERSON>ere bestanden uploaden <PERSON>taan", "Storage": "Opslagruimte", "Storages": "Opslagruimtes", "Storage name": "Opslagnaam", "Storage type": "Opslagtype", "Default storage": "Standaard opslagruimte", "Access base URL": "Toegangsbasis-URL", "Base URL for file access, could be your CDN base URL. For example: \"https://cdn.nocobase.com\".": "Basis-URL voor bestandstoegang, kan uw CDN-basis-URL zijn. Bijvoorbeeld: \"https://cdn.nocobase.com\".", "Destination": "Best<PERSON>mingsmap", "Use the built-in static file server": "Gebruik de ingebouwde statische bestandsserver", "Local storage": "Lokale opslag", "Aliyun OSS": "Aliyun OSS", "Amazon S3": "Amazon S3", "Tencent COS": "Tencent COS", "Region": "Regio", "Bucket": "Bucket", "Path": "Pad", "Relative path the file will be saved to. Left blank as root path. The leading and trailing slashes \"/\" will be ignored. For example: \"user/avatar\".": "Relatief pad waar het bestand zal worden opgeslagen. Laat leeg als hoofdmap. De voorloop- en achterloop-slashes \"/\" worden genegeerd. Bijvoorbeeld: \"gebruiker/avatar\".", "Filename": "Bestandsnaam", "Will be used for API": "Zal worden gebruikt voor API", "Default storage will be used when not selected": "Standaard opslagruimte wordt gebruikt wanneer niet geselecteerd", "Keep file in storage when destroy record": "Bestand in opslag houden bij vernietiging van record", "See more": "<PERSON><PERSON><PERSON>er", "Aliyun OSS region part of the bucket. For example: \"oss-cn-beijing\".": "Aliyun OSS regio-onderdeel van de bucket. Bijvoorbeeld: \"oss-cn-beijing\"."}