{"Verification": "Verification", "Verification providers": "Verification providers", "Provider type": "Provider type", "Aliyun SMS": "Aliyun SMS", "Access Key ID": "Access Key ID", "Access Key Secret": "Access Key Secret", "Endpoint": "Endpoint", "Sign": "Sign", "Template code": "Template code", "Secret Id": "Secret Id", "Secret Key": "Secret Key", "Region": "Region", "Sign name": "Sign name", "Sms sdk app id": "Sms sdk app id", "Template Id": "Template Id", "Verification send failed, please try later or contact to administrator": "Verification send failed, please try later or contact to administrator", "Not a valid cellphone number, please re-enter": "Not a valid cellphone number, please re-enter", "Please don't retry in {{time}} seconds": "Please don't retry in {{time}} seconds", "You are trying so frequently, please slow down": "You are trying so frequently, please slow down", "Verification code is invalid": "Verification code is invalid", "SMS OTP": "SMS OTP", "Get one-time codes sent to your phone via SMS to complete authentication requests.": "Get one-time codes sent to your phone via SMS to complete authentication requests.", "Unbind": "Unbind", "Bind": "Bind", "Configured": "Configured", "Unbind verifier": "Unbind verifier", "Not configured": "Not configured", "Unbound successfully": "Unbound successfully", "Bound successfully": "Bound successfully", "Verification type": "Verification type", "Provider": "Provider", "Verifier": "Verifier", "Verifiers": "Verifiers", "The following types of verifiers are available:": "The following types of verifiers are available: ", "Go to": "Go to", "create verifiers": "create verifiers", "Too many failed attempts. Please request a new verification code.": "Too many failed attempts. Please request a new verification code."}