{"name": "@nocobase/plugin-workflow-request", "displayName": "Workflow: HTTP request node", "displayName.zh-CN": "工作流：HTTP 请求节点", "description": "Send HTTP requests to any HTTP service for data interaction in workflow.", "description.zh-CN": "可用于在工作流中向任意 HTTP 服务发送请求，进行数据交互。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow-request", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow-request", "devDependencies": {"antd": "5.x", "axios": "^1.7.0", "react": "18.x", "react-i18next": "^11.15.1"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/plugin-workflow": ">=0.17.0-alpha.3", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Workflow"]}