{"name": "@nocobase/plugin-workflow-aggregate", "displayName": "Workflow: Aggregate node", "displayName.zh-CN": "工作流：聚合查询节点", "description": "Used to aggregate data against the database in workflow, such as: statistics, sum, average, etc.", "description.zh-CN": "可用于在工作流中对数据库进行聚合查询，如：统计数量、求和、平均值等。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow-aggregate", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow-aggregate", "devDependencies": {"antd": "5.x", "mathjs": "^10.6.0", "react": "18.x", "react-i18next": "^11.15.1"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/plugin-workflow": ">=0.17.0-alpha.3", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Workflow"]}