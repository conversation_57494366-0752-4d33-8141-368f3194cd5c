{"name": "@nocobase/plugin-workflow-notification", "displayName": "Workflow: notification node", "displayName.zh-CN": "工作流：通知节点", "description": "Send notification in workflow.", "description.zh-CN": "可用于在工作流中发送各类通知。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow-smtp-mailer", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow-smtp-mailer", "devDependencies": {"antd": "5.x", "react": "18.x"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/plugin-workflow": ">=0.17.0-alpha.3", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "keywords": ["Workflow"]}