{"name": "@nocobase/plugin-workflow-response-message", "version": "1.7.10", "displayName": "Workflow: Response message", "displayName.zh-CN": "工作流：响应消息", "description": "Used for assemble response message and showing to client in form event and request interception workflows.", "description.zh-CN": "用于在表单事件和请求拦截工作流中组装并向客户端显示响应消息。", "main": "dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow-response-message", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow-response-message", "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/plugin-workflow": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "keywords": ["Workflow"], "gitHead": "080fc78c1a744d47e010b3bbe5840446775800e4"}