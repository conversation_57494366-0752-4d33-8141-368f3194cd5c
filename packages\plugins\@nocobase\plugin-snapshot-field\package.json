{"name": "@nocobase/plugin-snapshot-field", "displayName": "Collection field: S<PERSON>shot", "displayName.zh-CN": "数据表字段：关系快照", "description": "When adding a new record, create a snapshot for its relational record and save in the new record. The snapshot will not be updated when the relational record is updated.", "description.zh-CN": "在添加数据时，为它的关系数据创建快照，并保存在当前的数据中。关系数据更新时，快照不会更新。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/field-snapshot", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/field-snapshot", "devDependencies": {"@ant-design/icons": "5.x", "@formily/core": "2.x", "@formily/react": "2.x", "@formily/shared": "2.x", "antd": "5.x", "react": "18.x", "react-i18next": "^11.15.1"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Collection fields"]}