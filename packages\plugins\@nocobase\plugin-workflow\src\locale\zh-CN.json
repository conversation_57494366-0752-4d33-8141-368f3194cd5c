{"Workflow": "工作流", "Execution history": "执行历史", "Clear all executions": "清除所有执行记录", "Clear executions will not reset executed count, and started executions will not be deleted, are you sure you want to delete them all?": "清空执行记录不会重置执行次数，且执行中的也不会被删除，确定要删除所有执行记录吗？", "Executed": "已执行", "Sync": "同步", "Sync enabled status of all workflows from database": "从数据库同步所有工作流的启用状态", "Trigger type": "触发方式", "Status": "状态", "On": "启用", "Off": "停用", "Category": "分类", "Add category": "添加分类", "Edit category": "编辑分类", "Delete category": "删除分类", "Version": "版本", "Copy to new version": "复制到新版本", "Duplicate": "复制", "Duplicate to new workflow": "复制为新工作流", "This is a main version, delete it will cause the whole workflow to be deleted (including all other revisions).": "当前是主版本，删除将导致整个流程被删除（包括所有其他版本）。", "Current version will be deleted (without affecting other versions).": "当前版本将被删除（不影响其他版本）。", "Execute manually": "手动执行", "The trigger is not configured correctly, please check the trigger configuration.": "触发器配置不正确，请检查触发器配置。", "This type of trigger has not been supported to be executed manually.": "该类型的触发器暂未支持手动执行。", "Trigger variables need to be filled for executing.": "执行需要填写触发器变量。", "A new version will be created automatically after execution if current version is not executed.": "如果当前版本还未执行过，将在执行后自动创建一个新版本。", "This will perform all the actions configured in the workflow. Are you sure you want to continue?": "将按照工作流中配置的所有操作执行，确定继续吗？", "Automatically create a new version after execution": "执行后自动创建新版本", "Workflow executed, the result status is <1>{{statusText}}</1><2>View the execution</2>": "工作流已执行，结果状态为 <1>{{statusText}}</1><2>查看执行详情</2>", "Loading": "加载中", "Load failed": "加载失败", "Use transaction": "启用事务", "Data operation nodes in workflow will run in a same transaction until any interruption. Any failure will cause data rollback, and will also rollback the history of the execution.": "工作流中的节点将在同一个事务中运行。任何失败都会导致数据回滚，同时也会回滚相应的执行历史。", "Auto delete history when execution is on end status": "执行结束后自动删除对应状态的历史记录", "Maximum number of cycling triggers": "最大循环触发次数", "The triggers of same workflow by some node (create, update and sub-flow etc.) more than this number will be ignored. Large number may cause performance issues. Please use with caution.": "由某个节点（创建、更新和子流程等）触发同一个工作流超过这个次数时将被忽略。设置过高的次数可能会导致性能问题，请谨慎使用。", "Continue when disabled or upgraded": "工作流被禁用或升级到新版本继续处理在途流程", "If checked, all nodes in-progress could continue to be processed in execution of disabled workflow. Otherwise, all nodes in-progress will be aborted automatically.": "如果选中，在途的节点在禁用或升级到新版本的工作流中将继续处理。否则将自动取消执行。", "Trigger": "触发器", "Unknown trigger": "未知触发器", "Workflow with unknown type will cause error. Please delete it or check plugin which provide this type.": "未知类型的工作流会导致错误，请删除或检查提供该类型的插件。", "Trigger variables": "触发器变量", "Trigger data": "触发数据", "Trigger time": "触发时间", "Triggered at": "触发时间", "Execute mode": "执行模式", "Execute workflow asynchronously or synchronously based on trigger type, and could not be changed after created.": "基于触发类型异步或同步执行工作流，创建后不可更改。", "Asynchronously": "异步", "Synchronously": "同步", "Will be executed in the background as a queued task.": "将作为队列任务在后台执行。", "For user actions that require immediate feedback. Can not use asynchronous nodes in such mode, and it is not recommended to perform time-consuming operations under synchronous mode.": "适用于需要即时反馈的用户操作。不能在此模式下使用异步节点，并且不建议在同步模式下执行耗时的操作。", "Go back": "返回", "Bind workflows": "绑定工作流", "Support pre-action event (local mode), post-action event (local mode), and approval event here.": "此处支持“操作前事件（局部模式）”、“操作后事件（局部模式）”、“审批事件”。", "Workflow will be triggered directly once the button clicked, without data saving. Only supports to be bound with \"Custom action event\".": "按钮点击后直接触发工作流，但不会保存数据。仅支持绑定“自定义操作事件”。", "\"Submit to workflow\" to \"Post-action event\" is deprecated, please use \"Custom action event\" instead.": "“提交至工作流”到“操作后事件”的方式已被弃用，请使用“自定义操作事件”代替。", "Workflow will be triggered before deleting succeeded (only supports pre-action event in local mode).": "删除成功之前触发工作流（支持操作前事件）。", "Submit to workflow": "提交至工作流", "Add workflow": "添加工作流", "Select workflow": "选择工作流", "Trigger data context": "触发数据上下文", "Full form data": "完整表单数据", "Select context": "选择上下文", "Collection event": "数据表事件", "Triggered when data changes in the collection, such as after adding, updating, or deleting a record. Unlike \"Post-action event\", Collection event listens for data changes rather than HTTP requests. Unless you understand the exact meaning, it is recommended to use \"Post-action event\".": "当数据表中的数据发生变化时触发，比如新增、更新或删除一条数据后。与“操作后事件”不同，数据表事件监听数据变动而不是 HTTP 请求，除非你明白确切含义，否则推荐使用“操作后事件”。", "Trigger on": "触发时机", "After record added": "新增数据后", "After record updated": "更新数据后", "After record added or updated": "新增或更新数据后", "After record deleted": "删除数据后", "Changed fields": "发生变动的字段", "Triggered only if one of the selected fields changes. If unselected, it means that it will be triggered when any field changes. When record is added or deleted, any field is considered to have been changed.": "只有被选中的某个字段发生变动时才会触发。如果不选择，则表示任何字段变动时都会触发。新增或删除数据时，任意字段都被认为发生变动。", "Only triggers when match conditions": "满足以下条件才触发", "Preload associations": "预加载关联数据", "Please select the associated fields that need to be accessed in subsequent nodes. With more than two levels of to-many associations may cause performance issue, please use with caution.": "请选中需要在后续节点中被访问的关系字段。超过两层的对多关联可能会导致性能问题，请谨慎使用。", "Choose a record or primary key of a record in the collection to trigger.": "选择数据表中的一行记录或者记录的主键来触发。", "Schedule event": "定时任务", "Triggered according to preset time conditions. Suitable for one-time or periodic tasks, such as sending notifications and cleaning data on a schedule.": "按预设的时间条件定时触发。适用于一次性或周期性的任务，如定时发送通知、清理数据等。", "Trigger mode": "触发模式", "Based on certain date": "自定义时间", "Based on date field of collection": "根据数据表时间字段", "Starts on": "开始于", "Ends on": "结束于", "No end": "不结束", "Exactly at": "当时", "Repeat mode": "重复模式", "Repeat limit": "重复次数", "No limit": "不限", "Seconds": "秒", "Minutes": "分钟", "Hours": "小时", "Days": "天", "Weeks": "周", "Months": "月", "No repeat": "不重复", "Every": "每", "By minute": "按分钟", "By hour": "按小时", "By day": "按天", "By week": "按周", "By month": "按月", "By field": "数据表字段", "By custom date": "自定义时间", "Advanced": "高级模式", "Execute on": "执行时间", "Current time": "当前时间", "End": "结束", "Node result": "节点数据", "Variable key of node": "节点变量标识", "Scope variables": "局域变量", "Operator": "运算符", "Calculate an expression based on a calculation engine and obtain a value as the result. Variables in the upstream nodes can be used in the expression.": "基于计算引擎对一个表达式进行计算，并获得一个值作为结果。表达式中可以使用上游节点里的变量。", "String operation": "字符串", "System variables": "系统变量", "System time": "系统时间", "Date variables": "日期变量", "Date range": "日期范围", "Executed at": "执行于", "Queueing": "队列中", "On going": "进行中", "Resolved": "完成", "Pending": "待处理", "Failed": "失败", "Error": "出错", "Aborted": "已终止", "Canceled": "已取消", "Rejected": "已拒绝", "Retry needed": "需重试", "Completed": "已完成", "All": "全部", "View result": "查看结果", "Triggered but still waiting in queue to execute.": "已触发但仍在队列中等待执行。", "Started and executing, maybe waiting for an async callback (manual, delay etc.).": "已开始执行，可能在等待异步回调（人工、延时等）。", "Successfully finished.": "成功完成。", "Failed to satisfy node configurations.": "未满足节点配置造成的失败。", "Some node meets error.": "某个节点出错。", "Running of some node was aborted by program flow.": "某个节点被程序流程终止。", "Manually canceled whole execution when waiting.": "等待时被手动取消整个执行。", "Rejected from a manual node.": "被人工节点拒绝继续。", "General failed but should do another try.": "执行失败，需重试。", "Cancel the execution": "取消执行", "Are you sure you want to cancel the execution?": "确定要取消该执行吗？", "Operations": "操作", "This node contains branches, deleting will also be preformed to them, are you sure?": "节点包含分支，将同时删除其所有分支下的子节点，确定继续？", "Control": "流程控制", "Collection operations": "数据表操作", "Manual": "人工处理", "Extended types": "扩展类型", "Node type": "节点类型", "Unknown node": "未知节点", "Node with unknown type will cause error. Please delete it or check plugin which provide this type.": "未知类型的节点会导致错误，请删除或检查提供该类型的插件。", "Calculation": "计算", "Calculation engine": "计算引擎", "Basic": "基础", "Calculation expression": "计算表达式", "Expression syntax error": "表达式语法错误", "Syntax references: ": "语法参考：", "Calculation result": "计算结果", "True": "真", "False": "假", "concat": "连接", "Condition": "条件判断", "Based on boolean result of the calculation to determine whether to \"continue\" or \"exit\" the process, or continue on different branches of \"yes\" and \"no\".": "基于计算结果的真假来决定“继续”或“退出”流程，或者在“是”与“否”的分支上分别继续。", "Mode": "模式", "Continue when \"Yes\"": "“是”则继续", "Branch into \"Yes\" and \"No\"": "“是”和“否”分别继续", "Condition expression": "条件表达式", "Inside of \"Yes\" branch": "“是”分支内", "Inside of \"No\" branch": "“否”分支内", "Create record": "新增数据", "Add new record to a collection. You can use variables from upstream nodes to assign values to fields.": "向一个数据表中添加新的数据。可以使用上游节点里的变量为字段赋值。", "Update record": "更新数据", "Update records of a collection. You can use variables from upstream nodes as query conditions and field values.": "更新一个数据表中的数据。可以使用上游节点里的变量作为查询条件和数据值。", "Update mode": "更新模式", "Update in a batch": "批量更新", "Update one by one": "逐条更新", "Update all eligible data at one time, which has better performance when the amount of data is large. But association fields are not supported (unless foreign key in current collection), and the updated data will not trigger other workflows.": "一次性更新所有符合条件的数据，在数据量较大时有比较好的性能；但不支持关系字段的更新（除非是在当前表中的外键），被更新的数据也不会触发其他工作流。", "The updated data can trigger other workflows, and the audit log will also be recorded. But it is usually only applicable to several or dozens of pieces of data, otherwise there will be performance problems.": "被更新的数据可以再次触发其他工作流，也会记录更新日志；但通常只适用于数条或数十条数据，否则会有性能问题。", "Query record": "查询数据", "Query records from a collection. You can use variables from upstream nodes as query conditions.": "查询一个数据表中的数据。可以使用上游节点里的变量作为查询条件。", "Allow multiple records as result": "允许结果是多条数据", "If checked, when there are multiple records in the query result, an array will be returned as the result, which can be operated on one by one using a loop node. Otherwise, only one record will be returned.": "选中后，当查询结果有多条记录时，会返回数组作为结果，可以使用循环节点对它逐条操作；否则，仅返回一条数据。", "Result type": "结果类型", "Single record": "单条数据", "Multiple records": "多条数据", "The result will be an object of the first matching record only, or null if no matched record.": "结果是一个对象，仅为首条匹配的记录，或空值。", "The result will be an array containing matched records, or an empty one if no matching records. This can be used to be processed in a loop node.": "结果会是一个数组，包含匹配条件的记录，没有匹配记录则为空数组。可以通过循环节点逐个处理。", "Exit when query result is null": "查询结果为空时，退出流程", "Please select collection first": "请先选择数据表", "Only update records matching conditions": "只更新满足条件的数据", "Please add at least one condition": "请添加至少一个条件", "Unassigned fields will be set to default values, and those without default values will be set to null.": "未被赋值的字段将被设置为默认值，没有默认值的设置为空值。", "Delete record": "删除数据", "Delete records of a collection. Could use variables in workflow context as filter. All records match the filter will be deleted.": "删除一个数据表中的数据。可以使用上游节点里的变量作为过滤条件。所有满足条件的数据都将被删除。", "Executed workflow cannot be modified. Could be copied to a new version to modify.": "已经执行过的工作流不能被修改，可通过复制到新版本后再修改。", "Can not delete": "无法删除", "The result of this node has been referenced by other nodes ({{nodes}}), please remove the usage before deleting.": "该节点的执行结果已被其他节点（{{nodes}}）引用，删除前请先移除引用。", "End process": "结束流程", "End the process immediately, with set status.": "以设置的状态立即结束流程。", "End status": "结束状态", "Succeeded": "成功", "Test run": "测试执行", "Test run will do the actual data manipulating or API calling, please use with caution.": "测试执行会进行实际的数据操作或 API 调用，请谨慎使用。", "No variable": "无变量", "Add node": "添加节点", "Move all downstream nodes to": "将所有下游节点移至", "After end of branches": "分支结束后", "Inside of branch": "分支内", "Workflow todos": "流程待办", "New version enabled": "已启用新版本", "Workflow is not exists": "工作流不存在"}