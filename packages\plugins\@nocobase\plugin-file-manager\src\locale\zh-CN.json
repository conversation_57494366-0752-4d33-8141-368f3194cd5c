{"File collection": "文件数据表", "File name": "文件名", "Extension name": "扩展名", "Size": "文件大小", "File size limit": "文件大小限制", "Minimum from 1 byte, maximum up to 1GB.": "最小为 1 字节，最大为 1GB。", "MIME type": "MIME 类型", "File type (in MIME type format)": "文件类型（MIME 格式）", "Multi-types seperated with comma, for example: \"image/*\", \"image/png\", \"image/*, application/pdf\" etc.": "多个类型用逗号分隔，例如：\"image/*\", \"image/png\", \"image/*, application/pdf\" 等。", "URL": "URL", "File storage": "文件存储", "File manager": "文件管理器", "Attachment": "附件", "Allow uploading multiple files": "允许上传多个文件", "Storage": "存储空间", "Storages": "存储空间", "Storage name": "存储空间标识", "Storage type": "存储类型", "Default storage": "默认存储空间", "Base URL": "基础 URL", "Base URL for file access, could be your CDN base URL. For example: \"https://cdn.nocobase.com\".": "文件访问的基础 URL，可以是你的 CDN 基础 URL。例如：\"https://cdn.nocobase.com\"。", "Destination": "上传目标文件夹", "Use the built-in static file server": "使用内置静态文件服务", "Local storage": "本地存储", "Aliyun OSS": "阿里云 OSS", "Amazon S3": "亚马逊 S3", "Tencent COS": "腾讯云 COS", "Region": "区域", "Bucket": "存储桶", "Path": "路径", "Relative path the file will be saved to. Left blank as root path. The leading and trailing slashes \"/\" will be ignored. For example: \"user/avatar\".": "文件保存的相对路径。留空则为根路径。开始和结尾的斜杠“/”会被忽略。例如：\"user/avatar\"。", "Filename": "文件名", "Will be used for API": "将用于 API", "Default storage will be used when not selected": "留空将使用默认存储空间", "Keep file in storage when destroy record": "删除记录时保留文件", "See more": "更多请查阅", "Aliyun OSS region part of the bucket. For example: \"oss-cn-beijing\".": "阿里云 OSS 存储桶所在区域。例如：\"oss-cn-beijing\"。", "Timeout": "超时时间", "Upload timeout for a single file in milliseconds. Default is 600000.": "上传单个文件的超时时间，单位为毫秒。默认为 600000。"}