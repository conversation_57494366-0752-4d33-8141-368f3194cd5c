{"name": "@nocobase/preset-nocobase", "version": "1.7.10", "license": "AGPL-3.0", "main": "./lib/server/index.js", "dependencies": {"@formily/json-schema": "2.x", "@nocobase/plugin-acl": "1.7.10", "@nocobase/plugin-action-bulk-edit": "1.7.10", "@nocobase/plugin-action-bulk-update": "1.7.10", "@nocobase/plugin-action-custom-request": "1.7.10", "@nocobase/plugin-action-duplicate": "1.7.10", "@nocobase/plugin-action-export": "1.7.10", "@nocobase/plugin-action-import": "1.7.10", "@nocobase/plugin-action-print": "1.7.10", "@nocobase/plugin-ai": "1.7.10", "@nocobase/plugin-api-doc": "1.7.10", "@nocobase/plugin-api-keys": "1.7.10", "@nocobase/plugin-async-task-manager": "1.7.10", "@nocobase/plugin-audit-logs": "1.7.10", "@nocobase/plugin-auth": "1.7.10", "@nocobase/plugin-auth-sms": "1.7.10", "@nocobase/plugin-backup-restore": "1.7.10", "@nocobase/plugin-block-iframe": "1.7.10", "@nocobase/plugin-block-template": "1.7.10", "@nocobase/plugin-block-workbench": "1.7.10", "@nocobase/plugin-calendar": "1.7.10", "@nocobase/plugin-charts": "1.7.10", "@nocobase/plugin-client": "1.7.10", "@nocobase/plugin-collection-sql": "1.7.10", "@nocobase/plugin-collection-tree": "1.7.10", "@nocobase/plugin-data-source-main": "1.7.10", "@nocobase/plugin-data-source-manager": "1.7.10", "@nocobase/plugin-data-visualization": "1.7.10", "@nocobase/plugin-departments": "1.7.10", "@nocobase/plugin-environment-variables": "1.7.10", "@nocobase/plugin-error-handler": "1.7.10", "@nocobase/plugin-field-attachment-url": "1.7.10", "@nocobase/plugin-field-china-region": "1.7.10", "@nocobase/plugin-field-formula": "1.7.10", "@nocobase/plugin-field-m2m-array": "1.7.10", "@nocobase/plugin-field-markdown-vditor": "1.7.10", "@nocobase/plugin-field-sequence": "1.7.10", "@nocobase/plugin-field-sort": "1.7.10", "@nocobase/plugin-file-manager": "1.7.10", "@nocobase/plugin-gantt": "1.7.10", "@nocobase/plugin-graph-collection-manager": "1.7.10", "@nocobase/plugin-kanban": "1.7.10", "@nocobase/plugin-locale-tester": "1.7.10", "@nocobase/plugin-localization": "1.7.10", "@nocobase/plugin-logger": "1.7.10", "@nocobase/plugin-map": "1.7.10", "@nocobase/plugin-mobile": "1.7.10", "@nocobase/plugin-mobile-client": "1.7.10", "@nocobase/plugin-mock-collections": "1.7.10", "@nocobase/plugin-multi-app-manager": "1.7.10", "@nocobase/plugin-multi-app-share-collection": "1.7.10", "@nocobase/plugin-notification-email": "1.7.10", "@nocobase/plugin-notification-in-app-message": "1.7.10", "@nocobase/plugin-notification-manager": "1.7.10", "@nocobase/plugin-public-forms": "1.7.10", "@nocobase/plugin-snapshot-field": "1.7.10", "@nocobase/plugin-system-settings": "1.7.10", "@nocobase/plugin-theme-editor": "1.7.10", "@nocobase/plugin-ui-schema-storage": "1.7.10", "@nocobase/plugin-user-data-sync": "1.7.10", "@nocobase/plugin-users": "1.7.10", "@nocobase/plugin-verification": "1.7.10", "@nocobase/plugin-workflow": "1.7.10", "@nocobase/plugin-workflow-action-trigger": "1.7.10", "@nocobase/plugin-workflow-aggregate": "1.7.10", "@nocobase/plugin-workflow-delay": "1.7.10", "@nocobase/plugin-workflow-dynamic-calculation": "1.7.10", "@nocobase/plugin-workflow-loop": "1.7.10", "@nocobase/plugin-workflow-mailer": "1.7.10", "@nocobase/plugin-workflow-manual": "1.7.10", "@nocobase/plugin-workflow-notification": "1.7.10", "@nocobase/plugin-workflow-parallel": "1.7.10", "@nocobase/plugin-workflow-request": "1.7.10", "@nocobase/plugin-workflow-response-message": "1.7.10", "@nocobase/plugin-workflow-sql": "1.7.10", "@nocobase/server": "1.7.10", "cronstrue": "^2.11.0", "fs-extra": "^11.1.1"}, "deprecated": ["@nocobase/plugin-audit-logs", "@nocobase/plugin-charts", "@nocobase/plugin-mobile-client", "@nocobase/plugin-snapshot-field"], "builtIn": ["@nocobase/plugin-acl", "@nocobase/plugin-action-bulk-edit", "@nocobase/plugin-action-bulk-update", "@nocobase/plugin-action-custom-request", "@nocobase/plugin-action-duplicate", "@nocobase/plugin-action-export", "@nocobase/plugin-action-import", "@nocobase/plugin-action-print", "@nocobase/plugin-auth", "@nocobase/plugin-async-task-manager", "@nocobase/plugin-ai", "@nocobase/plugin-block-iframe", "@nocobase/plugin-block-workbench", "@nocobase/plugin-calendar", "@nocobase/plugin-client", "@nocobase/plugin-collection-sql", "@nocobase/plugin-collection-tree", "@nocobase/plugin-data-source-main", "@nocobase/plugin-data-source-manager", "@nocobase/plugin-data-visualization", "@nocobase/plugin-environment-variables", "@nocobase/plugin-error-handler", "@nocobase/plugin-field-formula", "@nocobase/plugin-field-sequence", "@nocobase/plugin-field-sort", "@nocobase/plugin-file-manager", "@nocobase/plugin-gantt", "@nocobase/plugin-kanban", "@nocobase/plugin-logger", "@nocobase/plugin-notification-manager", "@nocobase/plugin-notification-in-app-message", "@nocobase/plugin-mobile", "@nocobase/plugin-system-settings", "@nocobase/plugin-ui-schema-storage", "@nocobase/plugin-user-data-sync", "@nocobase/plugin-users", "@nocobase/plugin-verification", "@nocobase/plugin-workflow", "@nocobase/plugin-workflow-action-trigger", "@nocobase/plugin-workflow-aggregate", "@nocobase/plugin-workflow-delay", "@nocobase/plugin-workflow-loop", "@nocobase/plugin-workflow-parallel", "@nocobase/plugin-workflow-request", "@nocobase/plugin-workflow-sql", "@nocobase/plugin-workflow-notification", "@nocobase/plugin-theme-editor", "@nocobase/plugin-block-template"], "repository": {"type": "git", "url": "git+https://github.com/nocobase/nocobase.git", "directory": "packages/presets/nocobase"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1"}