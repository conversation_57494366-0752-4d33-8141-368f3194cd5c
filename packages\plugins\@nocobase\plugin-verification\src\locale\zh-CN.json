{"Verification": "验证", "Verification providers": "验证码提供商", "Provider type": "提供商类型", "Aliyun SMS": "阿里云短信服务", "Tencent SMS": "腾讯云短信服务", "Access Key ID": "Access Key ID", "Access Key Secret": "Access Key Secret", "Endpoint": "接入点", "Sign": "签名", "Template code": "模板代码", "Secret Id": "Secret Id", "Secret Key": "Secret Key", "Region": "地域", "Sign name": "短信签名内容", "Sms sdk app id": "短信应用 ID", "Template Id": "短信模板 ID", "Verification send failed, please try later or contact to administrator": "验证码发送失败，请稍后重试或联系管理员", "Not a valid cellphone number, please re-enter": "不是有效的手机号，请重新输入", "Please don't retry in {{time}} seconds": "请 {{time}} 秒后再试", "You are trying so frequently, please slow down": "您的操作太频繁，请稍后再试", "Verification code is invalid": "无效的验证码", "SMS OTP": "短信验证码", "Get one-time codes sent to your phone via SMS to complete authentication requests.": "获取一次性短信验证码，以完成身份验证请求。", "Unbind": "解绑", "Bind": "绑定", "Configured": "已配置", "Unbind verifier": "解绑验证器", "Not configured": "未配置", "Unbound successfully": "解绑成功", "Bound successfully": "绑定成功", "Verification type": "验证类型", "Provider": "服务商", "Verifier": "验证器", "Verifiers": "验证器", "The following types of verifiers are available:": "以下类型的验证器可选：", "Go to": "前往", "create verifiers": "创建验证器", "Too many failed attempts. Please request a new verification code.": "验证失败次数过多，请重新获取验证码。"}