{"name": "@nocobase/plugin-workflow-action-trigger", "displayName": "Workflow: Post-action event", "displayName.zh-CN": "工作流：操作后事件", "description": "Triggered after the completion of a request initiated through an action button or API, such as after adding, updating, deleting data, or \"submit to workflow\". Suitable for data processing, sending notifications, etc., after actions are completed.", "description.zh-CN": "通过操作按钮或 API 发起请求并在执行完成后触发，比如新增、更新、删除数据或者“提交至工作流”之后。适用于在操作完成后进行数据处理、发送通知等。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/plugins/workflow-action-trigger", "homepage.zh-CN": "https://docs-cn.nocobase.com/plugins/workflow-action-trigger", "devDependencies": {"antd": "5.x", "react": "18.x", "react-i18next": "^11.15.1"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/plugin-workflow": ">=0.17.0-alpha.3", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Workflow"]}