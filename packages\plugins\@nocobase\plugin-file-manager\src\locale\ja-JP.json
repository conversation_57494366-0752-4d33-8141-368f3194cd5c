{"File manager": "ファイルストレージ", "Attachment": "添付ファイル", "MIME type": "ファイル形式", "Storage display name": "ファイルストレージ名", "Storage name": "ファイルストレージ識別子", "Storage type": "ストレージタイプ", "Default storage": "デフォルトストレージ", "Storage base URL": "Storage base URL", "Destination": "ファイルパス", "Use the built-in static file server": "組み込みの静的ファイル サービスを使用する", "Local storage": "ローカルストレージ", "Aliyun OSS": "Aliyun OSS", "Tencent COS": "Tencent COS", "Amazon S3": "Amazon S3", "Region": "地域", "Bucket": "バケット", "Path": "パス", "Filename": "ファイル名", "See more": "続きを見る", "Will be used for API": "API で使用されます", "File collection": "ファイルデータテーブル", "File name": "ファイル名", "Extension name": "拡張子", "Size": "ファイルサイズ", "File size limit": "ファイルサイズ制限", "Minimum from 1 byte, maximum up to 1GB.": "最小サイズは1バイト、最大サイズは1GBです。", "File type (in MIME type format)": "ファイルタイプ（MIME形式）", "Multi-types seperated with comma, for example: \"image/*\", \"image/png\", \"image/*, application/pdf\" etc.": "複数のタイプはカンマで区切ります。 例えば「image/*」「image/png」「image/*、application/pdf」など。", "URL": "URL", "File storage": "ファイルストレージ", "Allow uploading multiple files": "複数ファイルのアップロードを許可する", "Storage": "ストレージ", "Storages": "ストレージ", "Base URL": "ベースURL", "Base URL for file access, could be your CDN base URL. For example: \"https://cdn.nocobase.com\".": "术语调整，“基礎URL”改为“ベースURL”", "Relative path the file will be saved to. Left blank as root path. The leading and trailing slashes \"/\" will be ignored. For example: \"user/avatar\".": "更清晰的表达方式，符合技术描述", "Default storage will be used when not selected": "空欄の場合はデフォルトのストレージが使用されます。", "Keep file in storage when destroy record": "レコード削除時にファイルを保持", "Aliyun OSS region part of the bucket. For example: \"oss-cn-beijing\".": "阿里クラウドOSSの地域。 例えば「oss-cn-beijing」。"}