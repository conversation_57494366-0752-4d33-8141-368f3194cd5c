{"name": "@nocobase/plugin-workflow-delay", "displayName": "Workflow: Delay node", "displayName.zh-CN": "工作流：延时节点", "description": "Could be used in workflow parallel branch for waiting other branches.", "description.zh-CN": "可用于工作流并行分支中等待其他分支执行完成。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow-delay", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow-delay", "devDependencies": {"antd": "5.x", "react": "18.x", "react-i18next": "^11.15.1"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/plugin-workflow": ">=0.17.0-alpha.3", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Workflow"]}