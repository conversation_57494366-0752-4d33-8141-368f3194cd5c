{"Data source name": "Databron naam", "Data source display name": "Databron weergavenaam", "Host": "Host", "Port": "Poort", "Database": "Database", "Data source manager": "Databron manager", "Data sources": "Databronnen", "No external data source plugin installed": "Geen externe databron plugin geïnstalleerd", "View documentation": "Bekijk documentatie", "Test Connection": "Test verbinding", "Connection successful": "Verbinding succesvol", "Display name": "Weergavenaam", "Username": "Gebruikersnaam", "Password": "Wachtwoord", "Type": "Type", "Description": "Beschrijving", "Storage": "Opslag", "Collections": "Collecties", "Permissions": "<PERSON><PERSON><PERSON>", "Allow adding and modifying collection": "Toestaan toevoegen en wijzigen collectie", "Unknown field type": "Onbekend veldtype", "The following field types are not compatible and do not support output and display": "De volgende veldtypen zijn niet compatibel en ondersteunen geen uitvoer en weergave", "Field database type": "Veld database type", "Field interface": "Veld interface", "Status": "Status", "Loading": "Laden", "Failed": "Mislukt", "Loaded": "Geladen", "Reloading": "<PERSON><PERSON><PERSON>", "Data source synchronization in progress": "Databron synchronisatie in uitvoering", "Data source synchronization successful": "Databron synchronisatie succesvol", "Filter target key": "Filter doel sleutel", "Select field": "Selecteer veld", "OK": "OK", "Please select a field.": "Selecteer een veld.", "Are you sure you want to set the \"{{title}}\" field as a record unique key? This setting cannot be changed after it's been set.": "Weet je zeker dat je het veld \"{{title}}\" als een unieke sleutel wilt instellen? Deze instelling kan niet worden gewijzigd nadat deze is ingesteld.", "If a collection lacks a primary key, you must configure a unique record key to locate row records within a block, failure to configure this will prevent the creation of data blocks for the collection.": "Als een collectie geen primaire sleute<PERSON> heeft, moet je een unieke record sleutel configureren om rijrecords binnen een blok te lokaliseren, het niet configureren hiervan zal de creatie van datablokken voor de collectie voorkomen.", "Filter data based on the specific field, with the requirement that the field value must be unique.": "<PERSON><PERSON> gegevens op basis van het specifieke veld, met de eis dat de veldwaarde uniek moet zijn."}