{"name": "@nocobase/plugin-workflow", "displayName": "Workflow", "displayName.zh-CN": "工作流", "description": "A powerful BPM tool that provides foundational support for business automation, with the capability to extend unlimited triggers and nodes.", "description.zh-CN": "一个强大的 BPM 工具，为业务自动化提供基础支持，并且可任意扩展更多的触发器和节点。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow", "dependencies": {"@nocobase/plugin-workflow-test": "1.7.10"}, "devDependencies": {"@ant-design/icons": "5.x", "@formily/antd-v5": "1.x", "@formily/core": "2.x", "@formily/react": "2.x", "@types/ejs": "^3.1.1", "antd": "5.x", "classnames": "^2.3.1", "cron-parser": "4.4.0", "dayjs": "^1.11.8", "lodash": "4.17.21", "lru-cache": "8.0.5", "nodejs-snowflake": "2.0.1", "react": "18.x", "react-i18next": "^11.15.1", "react-js-cron": "^3.1.0", "react-router-dom": "^6.11.2", "sequelize": "^6.26.0", "tinybench": "4.x"}, "peerDependencies": {"@nocobase/actions": "1.x", "@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/evaluators": "1.x", "@nocobase/logger": "1.x", "@nocobase/plugin-data-source-main": "1.x", "@nocobase/plugin-error-handler": "1.x", "@nocobase/plugin-users": "1.x", "@nocobase/resourcer": "1.x", "@nocobase/server": "1.x", "@nocobase/test": "1.x", "@nocobase/utils": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Workflow"]}