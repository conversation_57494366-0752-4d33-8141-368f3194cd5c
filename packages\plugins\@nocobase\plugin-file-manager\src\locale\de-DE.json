{"File collection": "Dateisammlung", "File name": "Dateiname", "Extension name": "Erweiterungsname", "Size": "Größe", "File size limit": "Dateigrößenbegrenzung", "Minimum from 1 byte, maximum up to 1GB.": "Minimum 1 Byte, Maximum bis zu 1GB.", "File manager": "<PERSON><PERSON><PERSON>", "Attachment": "<PERSON><PERSON>", "MIME type": "MIME-Typ", "File type (in MIME type format)": "Dateityp (im MIME-Typ-Format)", "Multi-types seperated with comma, for example: \"image/*\", \"image/png\", \"image/*, application/pdf\" etc.": "Mehrere Typen durch Kommas getrennt, zum Beispiel: \"image/*\", \"image/png\", \"image/*, application/pdf\" usw.", "URL": "URL", "File storage": "<PERSON>ispe<PERSON><PERSON>", "Allow uploading multiple files": "<PERSON><PERSON>laden mehrerer <PERSON> erlauben", "Storage": "<PERSON><PERSON><PERSON><PERSON>", "Storages": "<PERSON><PERSON><PERSON><PERSON>", "Storage display name": "Anzeigename des Speichers", "Storage name": "Speichername", "Storage type": "Speichertyp", "Default storage": "Standardspeicher", "Storage base URL": "Basis-URL des Speichers", "Base URL": "Basis-URL", "Base URL for file access, could be your CDN base URL. For example: \"https://cdn.nocobase.com\".": "Basis-URL für den Dateizugriff, könnte Ihre CDN-Basis-URL sein. Zum Beispiel: \"https://cdn.nocobase.com\".", "Destination": "<PERSON><PERSON>", "Use the built-in static file server": "Den integrierten statischen Dateiserver verwenden", "Local storage": "Lokaler Speicher", "Aliyun OSS": "Aliyun OSS", "Amazon S3": "Amazon S3", "Tencent COS": "Tencent COS", "Region": "Region", "Bucket": "Bucket", "Path": "Pfad", "Relative path the file will be saved to. Left blank as root path. The leading and trailing slashes \"/\" will be ignored. For example: \"user/avatar\".": "Re<PERSON><PERSON> <PERSON><PERSON><PERSON>, in dem die Datei gespeichert wird. <PERSON><PERSON> lassen als Stammpfad. Die führenden und abschließenden Schrägstriche \"/\" werden ignoriert. Zum Beispiel: \"user/avatar\".", "Filename": "Dateiname", "Will be used for API": "Wird für API verwendet", "Default storage will be used when not selected": "<PERSON>n nicht ausgew<PERSON>hlt, wird der Standardspeicher verwendet", "Keep file in storage when destroy record": "Datei im Speicher behalten, wenn Datensatz gelöscht wird", "See more": "<PERSON><PERSON> anzeigen", "Aliyun OSS region part of the bucket. For example: \"oss-cn-beijing\".": "Aliyun OSS-Regionsteil des Buckets. Zum Beispiel: \"oss-cn-beijing\"."}