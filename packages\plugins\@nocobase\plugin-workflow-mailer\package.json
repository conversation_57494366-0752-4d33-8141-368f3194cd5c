{"name": "@nocobase/plugin-workflow-mailer", "displayName": "Workflow: mailer node", "displayName.zh-CN": "工作流：邮件发送节点", "description": "Send email in workflow.", "description.zh-CN": "可用于在工作流中发送电子邮件。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow-smtp-mailer", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow-smtp-mailer", "devDependencies": {"antd": "5.x", "nodemailer": "6.9.13", "react": "18.x"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/plugin-workflow": ">=0.17.0-alpha.3", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["NocoBase", "Workflow", "SMTP", "email"]}