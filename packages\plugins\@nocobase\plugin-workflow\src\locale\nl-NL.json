{"Workflow": "Workflow", "Execution history": "Execution history", "Executed": "Executed", "Trigger type": "Trigger type", "Status": "Status", "On": "<PERSON><PERSON>", "Off": "Uit", "Version": "<PERSON><PERSON><PERSON>", "Copy to new version": "Copy to new version", "Duplicate": "<PERSON><PERSON><PERSON><PERSON>", "Loading": "Loading", "Load failed": "Load failed", "Trigger": "<PERSON><PERSON>", "Trigger variables": "Trigger variables", "Trigger data": "Trigger data", "Trigger time": "Trigger time", "Triggered at": "Triggered at", "Collection event": "Collection event", "Trigger on": "<PERSON>gger on", "After record added": "After record added", "After record updated": "After record updated", "After record added or updated": "After record added or updated", "After record deleted": "After record deleted", "Changed fields": "Changed fields", "Triggered only if one of the selected fields changes. If unselected, it means that it will be triggered when any field changes. When record is added or deleted, any field is considered to have been changed.": "Triggered only if one of the selected fields changes. If unselected, it means that it will be triggered when any field changes. When record is added or deleted, any field is considered to have been changed.", "Only triggers when match conditions": "Only triggers when match conditions", "Schedule event": "Schedule event", "Trigger mode": "Trigger mode", "Based on certain date": "Based on certain date", "Based on date field of collection": "Based on date field of collection", "Starts on": "Starts on", "Ends on": "Ends on", "No end": "No end", "Exactly at": "Exactly at", "Repeat mode": "Repeat mode", "Repeat limit": "Repeat limit", "No limit": "No limit", "Seconds": "Seconds", "Minutes": "Minutes", "Hours": "Hours", "Days": "Days", "Weeks": "Weeks", "Months": "Months", "No repeat": "No repeat", "Every": "Every", "By minute": "By minute", "By hour": "By hour", "By day": "By day", "By week": "By week", "By month": "By month", "By field": "By field", "By custom date": "By custom date", "Advanced": "Advanced", "End": "End", "Node result": "Node result", "Constant": "Constant", "Null": "<PERSON><PERSON>", "Boolean": "Boolean", "String": "String", "Operator": "Operator", "Arithmetic calculation": "Arithmetic calculation", "String operation": "String operation", "Executed at": "Executed at", "Queueing": "Queueing", "On going": "On going", "Succeeded": "Succeeded", "Failed": "Failed", "Pending": "Pending", "Canceled": "Canceled", "This node contains branches, deleting will also be preformed to them, are you sure?": "This node contains branches, deleting will also be preformed to them, are you sure?", "Control": "Control", "Collection operations": "Collection operations", "Extended types": "Extended types", "Node type": "Node type", "Calculation": "Calculation", "Configure calculation": "Configure calculation", "Calculation result": "Calculation result", "True": "True", "False": "False", "concat": "concat", "Condition": "Voorwaarde", "Mode": "Mode", "Continue when \"Yes\"": "Continue when \"Yes\"", "Branch into \"Yes\" and \"No\"": "Branch into \"Yes\" and \"No\"", "Conditions": "Conditions", "Create record": "Create record", "Update record": "Record bijwerken", "Query record": "Query record", "Multiple records": "Multiple records", "Please select collection first": "Please select collection first", "Only update records matching conditions": "Only update records matching conditions", "Fields that are not assigned a value will be set to the default value, and those that do not have a default value are set to null.": "Fields that are not assigned a value will be set to the default value, and those that do not have a default value are set to null.", "Trigger in executed workflow cannot be modified": "Trigger in executed workflow cannot be modified", "Node in executed workflow cannot be modified": "Node in executed workflow cannot be modified", "Can not delete": "Can not delete", "The result of this node has been referenced by other nodes ({{nodes}}), please remove the usage before deleting.": "The result of this node has been referenced by other nodes ({{nodes}}), please remove the usage before deleting.", "Maximum number of loop calls": "Maximum number of loop calls", "If the number of loop calls is too large, there will be performance issues.": "If the number of loop calls is too large, there will be performance issues."}