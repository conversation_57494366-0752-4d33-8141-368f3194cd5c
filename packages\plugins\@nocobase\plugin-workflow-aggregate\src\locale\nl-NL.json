{"Aggregate": "Totaal", "Counting, summing, finding maximum, minimum, and average values for multiple records of a collection or associated data of a record.": "Het tellen, op<PERSON><PERSON>, het vinden van maximale, minimale en gemiddelde waarden voor meerdere records van een collectie of geassocieerde gegevens van een record.", "Aggregate function": "Aggregatiefunctie", "Aggregator function": "Aggregatorfunctie", "Target": "<PERSON><PERSON>", "Target type": "Doeltype", "Data of collection or associated record": "<PERSON><PERSON><PERSON><PERSON> van <PERSON> of geassocieerd record", "Query result": "Queryresultaat"}