{"name": "@nocobase/plugin-workflow-loop", "displayName": "Workflow: Loop node", "displayName.zh-CN": "工作流：循环节点", "description": "Used to repeat the sub-process processing of each value in an array, and can also be used for fixed times of sub-process processing.", "description.zh-CN": "用于对一个数组中的每个值进行重复的子流程处理，也可用于固定次数的重复子流程处理。", "version": "1.7.10", "license": "AGPL-3.0", "main": "./dist/server/index.js", "homepage": "https://docs.nocobase.com/handbook/workflow-loop", "homepage.zh-CN": "https://docs-cn.nocobase.com/handbook/workflow-loop", "devDependencies": {"@ant-design/icons": "5.x", "react": "18.x", "react-i18next": "^11.15.1"}, "peerDependencies": {"@nocobase/client": "1.x", "@nocobase/database": "1.x", "@nocobase/evaluators": "1.x", "@nocobase/plugin-workflow": ">=0.17.0-alpha.3", "@nocobase/server": "1.x", "@nocobase/test": "1.x"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1", "keywords": ["Workflow"]}