{"Edit": "Bewerken", "Delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Submit": "<PERSON><PERSON>", "Actions": "Acties", "Title": "Titel", "Enable": "Inschakelen", "Chart": "<PERSON><PERSON>", "ChartV2": "GrafiekV2", "Charts": "Grafieken", "Configure": "Configureren", "Duplicate": "<PERSON><PERSON><PERSON><PERSON>", "Configure chart": "<PERSON><PERSON><PERSON><PERSON> gra<PERSON>k", "Transform": "Transformeer", "Chart type": "Grafiektype", "JSON config": "JSON configuratie", "Query": "Query", "Data": "Data", "Run query": "Query uit<PERSON>eren", "Measures": "Afmetingen", "Dimensions": "Dimensies", "Filter": "Filter", "Sort": "So<PERSON><PERSON>", "Limit": "<PERSON><PERSON>", "Offset": "Offset", "Enable cache": "<PERSON><PERSON>", "TTL (second)": "T<PERSON> (seconde)", "veld": "Veld", "Aggregation": "Aggregatie", "Alias": "<PERSON><PERSON>", "Format": "Form<PERSON><PERSON>", "The first 10 records of the query result:": "De eerste 10 records van het queryresultaat:", "Please run query to retrive data.": "<PERSON><PERSON>r query uit om gegevens op te halen.", "Type": "Type", "Add veld": "Voeg veld toe", "Add chart": "<PERSON><PERSON><PERSON> grafiek toe", "xField": "X veld", "yField": "Y veld", "seriesField": "Serie veld", "angleField": "Hoek veld", "colorField": "<PERSON><PERSON><PERSON> veld", "Line": "<PERSON><PERSON>", "Area": "<PERSON><PERSON><PERSON>", "Column": "<PERSON><PERSON><PERSON>", "Bar": "Bar", "Pie": "<PERSON><PERSON>", "Dual axes": "Dubbele assen", "Scatter": "<PERSON><PERSON><PERSON>", "Gauge": "Gauge", "Statistic": "Statistiek", "Currency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Percent": "Percentage", "Exponential": "Exponential", "Abbreviation": "Afkorting", "Please configure and run query": "Configureer en voer de query uit", "Please configure chart": "<PERSON><PERSON><PERSON><PERSON> gra<PERSON>k", "Are you sure to cancel?": "Ben je zeker dat je wil annuleren?", "You changes are not saved. If you click OK, your changes will be lost.": "Je wijzigingen worden niet opgeslagen. Als je op OK klikt, gaan je wijzigingen verloren.", "Same properties set in the form above will be overwritten by this JSON config.": "Sommige eigenschappen die in het bovenstaande formulier zijn ingeste<PERSON>, worden overschreven door deze JSON-configuratie.", "Built-in": "Ingebouwd", "Config reference: ": "Configuratie referentie: ", "Table": "<PERSON><PERSON>", "Sum": "Som", "Avg": "Gem", "Count": "Aantal", "Min": "Min", "Max": "Max", "Please select a chart type.": "Kies een grafiektype.", "Collection": "Col<PERSON><PERSON>", "isStack": "isStapel", "isPercent": "isPercentage", "isGroup": "isGroep", "smooth": "glad", "Expand": "Uitbreiden", "Current filter": "Huidige filter", "Add custom veld": "Aangepast veld toevoegen", "To filter with custom fields, use \"Current filter\" variables in the chart configuration.": "Als je wil filteren met aang<PERSON><PERSON><PERSON> velden, gebruik je de variabelen \"Current filter\" in de grafiekconfiguratie.", "Input": "Invoer", "Date range": "Datumbereik", "Time range": "Tijdsbereik", "Edit veld properties": "Bewerk veld eigenschappen", "Select a source veld to use metadata of the veld": "Selecteer een bronveld om metagegevens van het veld te gebruiken", "Original veld": "<PERSON>eel veld", "Transformation": "Transformatie", "Add transformation": "Transformatie toevoegen", "Container": "Container", "Show border": "<PERSON>n rand", "Transformation tip": "<PERSON>elden staan ​​meerdere transformaties toe, die sequentieel worden toegepast. Let op wijzigingen in het gegevenstype na elke transformatie. Drag-and-drop-functionaliteit maakt aanpassing van de transformatievolgorde mogelijk.", "Type conversion": "Type conversie", "Transformer": "Transformeer", "Size": "Grootte", "Width": "<PERSON><PERSON><PERSON>", "Height": "<PERSON><PERSON><PERSON>", "Aspect ratio": "Aspect ratio", "Fixed height": "Vaste hoogte", "Show background": "<PERSON><PERSON> a<PERSON>", "Show padding": "<PERSON>n opvulling", "Distinct": "Onderscheiden", "Auto refresh": "Auto vernieuwing"}