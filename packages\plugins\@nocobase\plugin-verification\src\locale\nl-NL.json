{"Verification": "Verificatie", "Verification providers": "Verificatieproviders", "Provider type": "Provider type", "Aliyun SMS": "Aliyun SMS", "Access Key ID": "Access Key ID", "Access Key Secret": "Access Key Secret", "Endpoint": "Endpoint", "Sign": "Handtekening", "Template code": "Sjablooncode", "Secret Id": "Secret Id", "Secret Key": "Secret Key", "Region": "Regio", "Sign name": "Handtekening naam", "Sms sdk app id": "Sms sdk app id", "Template Id": "Sjabloon Id", "Verification send failed, please try later or contact to administrator": "Verificatie verz<PERSON><PERSON> mi<PERSON>, probeer het later opnieuw of neem contact op met de beheerder", "Not a valid cellphone number, please re-enter": "<PERSON><PERSON> geldig telefoonnum<PERSON>, probeer opnieuw in te voeren", "Please don't retry in {{time}} seconds": "<PERSON><PERSON>r niet op<PERSON>uw binnen {{time}} seconden", "You are trying so frequently, please slow down": "<PERSON> probeert te vaak, probeer lang<PERSON>er", "Verification code is invalid": "Verificatiecode is ongeldig"}