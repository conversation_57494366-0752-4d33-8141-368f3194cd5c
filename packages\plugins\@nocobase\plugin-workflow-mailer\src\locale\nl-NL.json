{"Mailer": "Verzender", "Send email. You can use the variables in the upstream nodes as receivers, subject and content of the email.": "Stuur e-mail door de SMTP-service aan te roepen. Je kan de variabelen in de stroomopwaartse nodes gebruiken als ontvangers, onderwerp en inhoud van de e-mail.", "SMTP host": "SMTP host", "Port": "Poort", "Secure": "<PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Password": "Wachtwoord", "From": "<PERSON>", "To": "<PERSON>ar", "CC": "CC", "BCC": "BCC", "Add email address": "E-mailadres toe<PERSON>n", "Subject": "Onderwerp", "Content": "<PERSON><PERSON><PERSON>", "Content type": "Inhoudstype", "Plain text": "Platte tekst", "Ignore failed sending and continue workflow": "<PERSON><PERSON><PERSON> mislukte verzending en ga door met de workflow"}