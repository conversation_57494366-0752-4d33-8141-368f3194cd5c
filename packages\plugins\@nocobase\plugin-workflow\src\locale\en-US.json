{"Workflow": "Workflow", "Execution history": "Execution history", "Clear all executions": "Clear all executions", "Clear executions will not reset executed count, and started executions will not be deleted, are you sure you want to delete them all?": "Clear executions will not reset executed count, and started executions will not be deleted, are you sure you want to delete them all?", "Executed": "Executed", "Sync": "Sync", "Sync enabled status of all workflows from database": "Sync enabled status of all workflows from database", "Trigger type": "Trigger type", "Status": "Status", "On": "On", "Off": "Off", "Category": "Category", "Add category": "Add category", "Edit category": "Edit category", "Delete category": "Delete category", "Version": "Version", "Copy to new version": "Copy to new version", "Duplicate": "Duplicate", "Duplicate to new workflow": "Duplicate to new workflow", "This is a main version, delete it will cause the whole workflow to be deleted (including all other revisions).": "This is a main version, delete it will cause the whole workflow to be deleted (including all other revisions).", "Current version will be deleted (without affecting other versions).": "Current version will be deleted (without affecting other versions).", "Execute manually": "Execute manually", "The trigger is not configured correctly, please check the trigger configuration.": "The trigger is not configured correctly, please check the trigger configuration.", "This type of trigger has not been supported to be executed manually.": "This type of trigger has not been supported to be executed manually.", "Trigger variables need to be filled for executing.": "Trigger variables need to be filled for executing.", "A new version will be created automatically after execution if current version is not executed.": "A new version will be created automatically after execution if current version is not executed.", "This will perform all the actions configured in the workflow. Are you sure you want to continue?": "This will perform all the actions configured in the workflow. Are you sure you want to continue?", "Automatically create a new version after execution": "Automatically create a new version after execution", "Workflow executed, the result status is <1>{{statusText}}</1><2>View the execution</2>": "Workflow executed, the result status is <1>{{statusText}}</1><2>View the execution</2>", "Loading": "Loading", "Load failed": "Load failed", "Use transaction": "Use transaction", "Data operation nodes in workflow will run in a same transaction until any interruption. Any failure will cause data rollback, and will also rollback the history of the execution.": "Data operation nodes in workflow will run in a same transaction until any interruption. Any failure will cause data rollback, and will also rollback the history of the execution.", "Auto delete history when execution is on end status": "Auto delete history when execution is on end status", "Maximum number of cycling triggers": "Maximum number of cycling triggers", "The triggers of same workflow by some node (create, update and sub-flow etc.) more than this number will be ignored. Large number may cause performance issues. Please use with caution.": "The triggers of same workflow by some node (create, update and sub-flow etc.) more than this number will be ignored. Large number may cause performance issues. Please use with caution.", "Continue when disabled or upgraded": "Continue when disabled or upgraded", "If checked, all nodes in-progress could continue to be processed in execution of disabled workflow. Otherwise, all nodes in-progress will be aborted automatically.": "If checked, all nodes in-progress could continue to be processed in execution of disabled workflow. Otherwise, all nodes in-progress will be aborted automatically.", "Trigger": "<PERSON><PERSON>", "Unknown trigger": "Unknown trigger", "Workflow with unknown type will cause error. Please delete it or check plugin which provide this type.": "Workflow with unknown type will cause error. Please delete it or check plugin which provide this type.", "Trigger variables": "Trigger variables", "Trigger data": "Trigger data", "Trigger time": "Trigger time", "Triggered at": "Triggered at", "Execute mode": "Execute mode", "Execute workflow asynchronously or synchronously based on trigger type, and could not be changed after created.": "Execute workflow asynchronously or synchronously based on trigger type, and could not be changed after created.", "Asynchronously": "Asynchronously", "Synchronously": "Synchronously", "Will be executed in the background as a queued task.": "Will be executed in the background as a queued task.", "For user actions that require immediate feedback. Can not use asynchronous nodes in such mode, and it is not recommended to perform time-consuming operations under synchronous mode.": "For user actions that require immediate feedback. Can not use asynchronous nodes in such mode, and it is not recommended to perform time-consuming operations under synchronous mode.", "Go back": "Go back", "Bind workflows": "Bind workflows", "Support pre-action event (local mode), post-action event (local mode), and approval event here.": "Support pre-action event (local mode), post-action event (local mode), and approval event here.", "Workflow will be triggered directly once the button clicked, without data saving. Only supports to be bound with \"Custom action event\".": "Workflow will be triggered directly once the button clicked, without data saving. Only supports to be bound with \"Custom action event\".", "\"Submit to workflow\" to \"Post-action event\" is deprecated, please use \"Custom action event\" instead.": "\"Submit to workflow\" to \"Post-action event\" is deprecated, please use \"Custom action event\" instead.", "Workflow will be triggered before deleting succeeded (only supports pre-action event in local mode).": "Workflow will be triggered before deleting succeeded (only supports pre-action event in local mode).", "Submit to workflow": "Submit to workflow", "Add workflow": "Add workflow", "Select workflow": "Select workflow", "Trigger data context": "Trigger data context", "Full form data": "Full form data", "Select context": "Select context", "Collection event": "Collection event", "Triggered when data changes in the collection, such as after adding, updating, or deleting a record. Unlike \"Post-action event\", Collection event listens for data changes rather than HTTP requests. Unless you understand the exact meaning, it is recommended to use \"Post-action event\".": "Triggered when data changes in the collection, such as after adding, updating, or deleting a record. Unlike \"Post-action event\", Collection event listens for data changes rather than HTTP requests. Unless you understand the exact meaning, it is recommended to use \"Post-action event\".", "Trigger on": "<PERSON>gger on", "After record added": "After record added", "After record updated": "After record updated", "After record added or updated": "After record added or updated", "After record deleted": "After record deleted", "Changed fields": "Changed fields", "Triggered only if one of the selected fields changes. If unselected, it means that it will be triggered when any field changes. When record is added or deleted, any field is considered to have been changed.": "Triggered only if one of the selected fields changes. If unselected, it means that it will be triggered when any field changes. When record is added or deleted, any field is considered to have been changed.", "Only triggers when match conditions": "Only triggers when match conditions", "Preload associations": "Preload associations", "Please select the associated fields that need to be accessed in subsequent nodes. With more than two levels of to-many associations may cause performance issue, please use with caution.": "Please select the associated fields that need to be accessed in subsequent nodes. With more than two levels of to-many associations may cause performance issue, please use with caution.", "Choose a record or primary key of a record in the collection to trigger.": "Choose a record or primary key of a record in the collection to trigger.", "Schedule event": "Schedule event", "Triggered according to preset time conditions. Suitable for one-time or periodic tasks, such as sending notifications and cleaning data on a schedule.": "Triggered according to preset time conditions. Suitable for one-time or periodic tasks, such as sending notifications and cleaning data on a schedule.", "Trigger mode": "Trigger mode", "Based on certain date": "Based on certain date", "Based on date field of collection": "Based on date field of collection", "Starts on": "Starts on", "Ends on": "Ends on", "No end": "No end", "Exactly at": "Exactly at", "Repeat mode": "Repeat mode", "Repeat limit": "Repeat limit", "No limit": "No limit", "Seconds": "Seconds", "Minutes": "Minutes", "Hours": "Hours", "Days": "Days", "Weeks": "Weeks", "Months": "Months", "No repeat": "No repeat", "Every": "Every", "By minute": "By minute", "By hour": "By hour", "By day": "By day", "By week": "By week", "By month": "By month", "By field": "By field", "By custom date": "By custom date", "Advanced": "Advanced", "Execute on": "Execute on", "Current time": "Current time", "End": "End", "Node result": "Node result", "Variable key of node": "Variable key of node", "Scope variables": "Scope variables", "Operator": "Operator", "Calculate an expression based on a calculation engine and obtain a value as the result. Variables in the upstream nodes can be used in the expression.": "Calculate an expression based on a calculation engine and obtain a value as the result. Variables in the upstream nodes can be used in the expression.", "String operation": "String operation", "System variables": "System variables", "System time": "System time", "Date variables": "Date variables", "Date range": "Date range", "Executed at": "Executed at", "Queueing": "Queueing", "On going": "On going", "Resolved": "Resolved", "Pending": "Pending", "Failed": "Failed", "Error": "Error", "Aborted": "Aborted", "Canceled": "Canceled", "Rejected": "Rejected", "Retry needed": "Retry needed", "Completed": "Completed", "All": "All", "View result": "View result", "Triggered but still waiting in queue to execute.": "Triggered but still waiting in queue to execute.", "Started and executing, maybe waiting for an async callback (manual, delay etc.).": "Started and executing, maybe waiting for an async callback (manual, delay etc.).", "Successfully finished.": "Successfully finished.", "Failed to satisfy node configurations.": "Failed to satisfy node configurations.", "Some node meets error.": "Some node meets error.", "Running of some node was aborted by program flow.": "Running of some node was aborted by program flow.", "Manually canceled whole execution when waiting.": "Manually canceled whole execution when waiting.", "Rejected from a manual node.": "Rejected from a manual node.", "General failed but should do another try.": "General failed but should do another try.", "Cancel the execution": "Cancel the execution", "Are you sure you want to cancel the execution?": "Are you sure you want to cancel the execution?", "Operations": "Operations", "This node contains branches, deleting will also be preformed to them, are you sure?": "This node contains branches, deleting will also be preformed to them, are you sure?", "Control": "Control", "Collection operations": "Collection operations", "Manual": "Manual", "Extended types": "Extended types", "Node type": "Node type", "Unknown node": "Unknown node", "Node with unknown type will cause error. Please delete it or check plugin which provide this type.": "Node with unknown type will cause error. Please delete it or check plugin which provide this type.", "Calculation": "Calculation", "Calculation engine": "Calculation engine", "Basic": "Basic", "Calculation expression": "Calculation expression", "Expression syntax error": "Expression syntax error", "Syntax references: ": "Syntax references: ", "Calculation result": "Calculation result", "True": "True", "False": "False", "concat": "concat", "Condition": "Condition", "Based on boolean result of the calculation to determine whether to \"continue\" or \"exit\" the process, or continue on different branches of \"yes\" and \"no\".": "Based on boolean result of the calculation to determine whether to \"continue\" or \"exit\" the process, or continue on different branches of \"yes\" and \"no\".", "Mode": "Mode", "Continue when \"Yes\"": "Continue when \"Yes\"", "Branch into \"Yes\" and \"No\"": "Branch into \"Yes\" and \"No\"", "Condition expression": "Condition expression", "Inside of \"Yes\" branch": "Inside of \"Yes\" branch", "Inside of \"No\" branch": "Inside of \"No\" branch", "Create record": "Create record", "Add new record to a collection. You can use variables from upstream nodes to assign values to fields.": "Add new record to a collection. You can use variables from upstream nodes to assign values to fields.", "Update record": "Update record", "Update records of a collection. You can use variables from upstream nodes as query conditions and field values.": "Update records of a collection. You can use variables from upstream nodes as query conditions and field values.", "Update mode": "Update mode", "Update in a batch": "Update in a batch", "Update one by one": "Update one by one", "Update all eligible data at one time, which has better performance when the amount of data is large. But association fields are not supported (unless foreign key in current collection), and the updated data will not trigger other workflows.": "Update all eligible data at one time, which has better performance when the amount of data is large. But association fields are not supported (unless foreign key in current collection), and the updated data will not trigger other workflows.", "The updated data can trigger other workflows, and the audit log will also be recorded. But it is usually only applicable to several or dozens of pieces of data, otherwise there will be performance problems.": "The updated data can trigger other workflows, and the audit log will also be recorded. But it is usually only applicable to several or dozens of pieces of data, otherwise there will be performance problems.", "Query record": "Query record", "Query records from a collection. You can use variables from upstream nodes as query conditions.": "Query records from a collection. You can use variables from upstream nodes as query conditions.", "Allow multiple records as result": "Allow multiple records as result", "If checked, when there are multiple records in the query result, an array will be returned as the result, which can be operated on one by one using a loop node. Otherwise, only one record will be returned.": "If checked, when there are multiple records in the query result, an array will be returned as the result, which can be operated on one by one using a loop node. Otherwise, only one record will be returned.", "Result type": "Result type", "Single record": "Single record", "Multiple records": "Multiple records", "The result will be an object of the first matching record only, or null if no matched record.": "The result will be an object of the first matching record only, or null if no matched record.", "The result will be an array containing matched records, or an empty one if no matching records. This can be used to be processed in a loop node.": "The result will be an array containing matched records, or an empty one if no matching records. This can be used to be processed in a loop node.", "Exit when query result is null": "Exit when query result is null", "Please select collection first": "Please select collection first", "Only update records matching conditions": "Only update records matching conditions", "Please add at least one condition": "Please add at least one condition", "Unassigned fields will be set to default values, and those without default values will be set to null.": "Unassigned fields will be set to default values, and those without default values will be set to null.", "Delete record": "Delete record", "Delete records of a collection. Could use variables in workflow context as filter. All records match the filter will be deleted.": "Delete records of a collection. Could use variables in workflow context as filter. All records match the filter will be deleted.", "Executed workflow cannot be modified. Could be copied to a new version to modify.": "Executed workflow cannot be modified. Could be copied to a new version to modify.", "Can not delete": "Can not delete", "The result of this node has been referenced by other nodes ({{nodes}}), please remove the usage before deleting.": "The result of this node has been referenced by other nodes ({{nodes}}), please remove the usage before deleting.", "End process": "End process", "End the process immediately, with set status.": "End the process immediately, with set status.", "End status": "End status", "Succeeded": "Succeeded", "Test run": "Test run", "Test run will do the actual data manipulating or API calling, please use with caution.": "Test run will do the actual data manipulating or API calling, please use with caution.", "No variable": "No variable", "Add node": "Add node", "Move all downstream nodes to": "Move all downstream nodes to", "After end of branches": "After end of branches", "Inside of branch": "Inside of branch", "Workflow todos": "Workflow todos", "New version enabled": "New version enabled", "Workflow is not exists": "Workflow is not exists"}