{"Edit": "Edit", "Delete": "Delete", "Cancel": "Cancel", "Submit": "Submit", "Actions": "Actions", "Title": "Title", "Enable": "Enable", "Chart": "Chart", "ChartV2": "ChartV2", "Charts": "Charts", "Configure": "Configure", "Duplicate": "Duplicate", "Configure chart": "Configure chart", "Transform": "Transform", "Chart type": "Chart type", "JSON config": "JSON config", "Query": "Query", "Data": "Data", "Run query": "Run query", "Measures": "Measures", "Dimensions": "Dimensions", "Filter": "Filter", "Sort": "Sort", "Limit": "Limit", "Offset": "Offset", "Enable cache": "Enable cache", "TTL (second)": "<PERSON><PERSON> (second)", "Field": "Field", "Aggregation": "Aggregation", "Alias": "<PERSON><PERSON>", "Format": "Format", "The first 10 records of the query result:": "The first 10 records of the query result:", "Please run query to retrive data.": "Please run query to retrive data.", "Type": "Type", "Add field": "Add field", "Add chart": "Add chart", "xField": "X field", "yField": "Y field", "seriesField": "Series field", "angleField": "<PERSON>le field", "colorField": "Color field", "Line": "Line", "Area": "Area", "Column": "Column", "Bar": "Bar", "Pie": "Pie", "Dual axes": "Dual axes", "Scatter": "<PERSON><PERSON><PERSON>", "Gauge": "Gauge", "Statistic": "Statistic", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Percent": "Percent", "Exponential": "Exponential", "Abbreviation": "Abbreviation", "Please configure and run query": "Please configure and run query", "Please configure chart": "Please configure chart", "Are you sure to cancel?": "Are you sure to cancel?", "You changes are not saved. If you click OK, your changes will be lost.": "You changes are not saved. If you click OK, your changes will be lost.", "Same properties set in the form above will be overwritten by this JSON config.": "Same properties set in the form above will be overwritten by this JSON config.", "Built-in": "Built-in", "Config reference: ": "Config reference: ", "Table": "Table", "Sum": "Sum", "Avg": "Avg", "Count": "Count", "Min": "Min", "Max": "Max", "Please select a chart type.": "Please select a chart type.", "Collection": "Collection", "isStack": "isStack", "isPercent": "isPercent", "isGroup": "isGroup", "smooth": "smooth", "Expand": "Expand", "Current filter": "Current filter", "Add custom field": "Add custom field", "To filter with custom fields, use \"Current filter\" variables in the chart configuration.": "To filter with custom fields, use \"Current filter\" variables in the chart configuration.", "Input": "Input", "Date range": "Date range", "Time range": "Time range", "Edit field properties": "Edit field properties", "Select a source field to use metadata of the field": "Select a source field to use metadata of the field", "Original field": "Original field", "Transformation": "Transformation", "Add transformation": "Add transformation", "Container": "Container", "Show border": "Show border", "Transformation tip": "Fields allow multiple transformations, applied sequentially. Pay attention to data type changes after each transformation. Drag-and-drop functionality enables adjustment of transformation order.", "Type conversion": "Type conversion", "Transformer": "Transformer", "Size": "Size", "Width": "<PERSON><PERSON><PERSON>", "Height": "Height", "Aspect ratio": "Aspect ratio", "Fixed height": "Fixed height", "Show background": "Show background", "Show padding": "Show padding", "Distinct": "Distinct", "Auto refresh": "Auto refresh", "Divide": "Divide", "Multiply": "Multiply"}