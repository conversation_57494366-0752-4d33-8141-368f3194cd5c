{"File collection": "File collection", "File name": "File name", "Extension name": "Extension name", "Size": "Size", "File size limit": "File size limit", "Minimum from 1 byte, maximum up to 1GB.": "Minimum from 1 byte, maximum up to 1GB.", "MIME type": "MIME type", "File type (in MIME type format)": "File type (in MIME type format)", "Multi-types seperated with comma, for example: \"image/*\", \"image/png\", \"image/*, application/pdf\" etc.": "Multi-types seperated with comma, for example: \"image/*\", \"image/png\", \"image/*, application/pdf\" etc.", "URL": "URL", "File storage": "File storage", "File manager": "File manager", "Attachment": "Attachment", "Allow uploading multiple files": "Allow uploading multiple files", "Storage": "Storage", "Storages": "Storages", "Storage name": "Storage name", "Storage type": "Storage type", "Default storage": "Default storage", "Access base URL": "Access base URL", "Base URL for file access, could be your CDN base URL. For example: \"https://cdn.nocobase.com\".": "Base URL for file access, could be your CDN base URL. For example: \"https://cdn.nocobase.com\".", "Destination": "Destination", "Use the built-in static file server": "Use the built-in static file server", "Local storage": "Local storage", "Aliyun OSS": "Aliyun OSS", "Amazon S3": "Amazon S3", "Tencent COS": "Tencent COS", "Region": "Region", "Bucket": "Bucket", "Path": "Path", "Relative path the file will be saved to. Left blank as root path. The leading and trailing slashes \"/\" will be ignored. For example: \"user/avatar\".": "Relative path the file will be saved to. Left blank as root path. The leading and trailing slashes \"/\" will be ignored. For example: \"user/avatar\".", "Filename": "Filename", "Will be used for API": "Will be used for API", "Default storage will be used when not selected": "Default storage will be used when not selected", "Keep file in storage when destroy record": "Keep file in storage when destroy record", "See more": "See more", "Aliyun OSS region part of the bucket. For example: \"oss-cn-beijing\".": "Aliyun OSS region part of the bucket. For example: \"oss-cn-beijing\".", "Timeout": "Timeout", "Upload timeout for a single file in milliseconds. Default is 600000.": "Upload timeout for a single file in milliseconds. Default is 600000."}