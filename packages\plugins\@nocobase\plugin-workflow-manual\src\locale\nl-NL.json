{"Manual": "Manual", "Could be used for manually submitting data, and determine whether to continue or exit. Workflow will generate a todo item for assigned user when it reaches a manual node, and continue processing after user submits the form.": "Could be used for manually submitting data, and determine whether to continue or exit. Workflow will generate a todo item for assigned user when it reaches a manual node, and continue processing after user submits the form.", "Values preset in this form will override user submitted ones when continue or reject.": "Values preset in this form will override user submitted ones when continue or reject.", "Assignee": "Assignee", "Assignees": "Assignees", "User interface": "User interface", "Configure user interface": "Configure user interface", "View user interface": "View user interface", "Separately": "Separately", "Each user has own task": "Each user has own task", "Collaboratively": "Collaboratively", "Everyone shares one task": "Everyone shares one task", "Negotiation": "Negotiation", "All pass": "All pass", "Everyone should pass": "Everyone should pass", "Any pass": "Any pass", "Anyone pass": "Anyone pass", "Field name existed in form": "Field name existed in form", "Continue the process": "Continue the process", "Terminate the process": "Terminate the process", "Save temporarily": "Save temporarily", "Custom form": "Custom form", "Data record": "Data record", "Create record form": "Create record form", "Update record form": "Update record form", "Filter settings": "Filter settings", "Workflow todos": "Workflow todos", "Task": "Task"}