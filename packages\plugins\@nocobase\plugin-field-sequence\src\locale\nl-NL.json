{"Sequence": "<PERSON><PERSON><PERSON>", "Automatically generate codes based on configured rules, supporting combinations of dates, numbers, and text.": "Automatisch codes genereren op basis van geconfigureerde regels, met ondersteuning voor combinaties van datums, nummers en tekst.", "Sequence rules": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add rule": "<PERSON><PERSON><PERSON> regel toe", "Inputable": "<PERSON><PERSON><PERSON>ba<PERSON>", "Match rules": "<PERSON><PERSON><PERSON><PERSON>", "Type": "Type", "Autoincrement": "Automatisch verhogen", "Fixed text": "Vaste tekst", "Text content": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Date": "Datum", "Date format": "Datumformaat", "Supports all formats of the Day.js library, such as \"YYYYMMDD\", \"YYYY-MM-DD\", etc.": "Ondersteunt alle formaten van de Day.js-bibliotheek, zoals \"YYYYMMDD\", \"YYYY-MM-DD\", enz.", "Rule content": "Regelinhoud", "{{value}} Digits": "{{value}} cijfers", "Digits": "Cijfers", "Start from": "Start vanaf", "Starts from {{value}}": "Start vanaf {{value}}", "Reset cycle": "Cyclus resetten", "No reset": "<PERSON><PERSON> reset", "Daily": "Dagelijks", "Every Monday": "<PERSON><PERSON><PERSON> ma<PERSON>g", "Monthly": "Ma<PERSON><PERSON><PERSON><PERSON>", "Yearly": "Jaarlijks", "Operations": "Operaties", "Customize": "<PERSON><PERSON> aan", "Random character": "<PERSON><PERSON><PERSON><PERSON> karakter", "Length": "<PERSON><PERSON><PERSON>", "Will generate random characters with specified length.": "<PERSON><PERSON><PERSON> te<PERSON> met de opgegeven lengte.", "Character sets": "Karaktersets", "Select character sets to generate random characters.": "Selecteer karaktersets om willekeurige tekens te genereren.", "Number": "<PERSON><PERSON><PERSON>", "Lowercase letters": "Kleine letters", "Uppercase letters": "Hoofdletters", "Symbols": "Symbolen"}